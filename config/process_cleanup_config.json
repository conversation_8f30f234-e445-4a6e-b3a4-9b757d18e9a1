{"process_cleanup_config": {"common_user_apps": [{"package": "com.UCMobile", "description": "UC浏览器", "category": "browser"}, {"package": "com.accuweather", "description": "天气", "category": "tools"}, {"package": "com.aigallery.videoeditor", "description": "Clipper", "category": "media"}, {"package": "com.alibaba.android.rimet", "description": "钉钉", "category": "work"}, {"package": "com.android.aicoremodelmanager", "description": "Ai Core Model Manager", "category": "system_app"}, {"package": "com.android.aigallery", "description": "Ai Gallery", "category": "system_app"}, {"package": "com.android.aod", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.android.calendar", "description": "日历", "category": "system_app"}, {"package": "com.android.camera", "description": "相机", "category": "system_app"}, {"package": "com.android.childmode", "description": "Childmode", "category": "system_app"}, {"package": "com.android.chrome", "description": "Chrome浏览器", "category": "browser"}, {"package": "com.android.clipper", "description": "Clipper", "category": "system_app"}, {"package": "com.android.commoncontrol", "description": "Common Control", "category": "system_app"}, {"package": "com.android.contacts", "description": "联系人", "category": "system_app"}, {"package": "com.android.cutepet", "description": "Cute Pet", "category": "system_app"}, {"package": "com.android.demomode", "description": "Demo Mode", "category": "system_app"}, {"package": "com.android.deskclock", "description": "时钟", "category": "system_app"}, {"package": "com.android.dialer", "description": "联系人", "category": "system_app"}, {"package": "com.android.documentsui", "description": "文件管理器", "category": "system_app"}, {"package": "com.android.drivingmode", "description": "Driving Mode", "category": "system_app"}, {"package": "com.android.dynamicbar", "description": "Dynamic Bar", "category": "system_app"}, {"package": "com.android.ellascreenrecognition", "description": "Ella Screen Recognition", "category": "system_app"}, {"package": "com.android.filemanager", "description": "文件管理器", "category": "system_app"}, {"package": "com.android.finder", "description": "Finder", "category": "system_app"}, {"package": "com.android.flipexternalscreen", "description": "Flip External Screen", "category": "system_app"}, {"package": "com.android.flipmusic", "description": "Flipmusic", "category": "system_app"}, {"package": "com.android.fmradio", "description": "Fm Radio", "category": "system_app"}, {"package": "com.android.foldingscreenzone", "description": "Folding Screen Zone", "category": "system_app"}, {"package": "com.android.fwdualsystem", "description": "Fw Dual System", "category": "system_app"}, {"package": "com.android.globalsearch", "description": "Global Search", "category": "system_app"}, {"package": "com.android.globaltheme", "description": "Global Theme", "category": "system_app"}, {"package": "com.android.googlecontact", "description": "Google Contact", "category": "system_app"}, {"package": "com.android.googledefault", "description": "Google Default", "category": "system_app"}, {"package": "com.android.googlemessage", "description": "Google Message", "category": "system_app"}, {"package": "com.android.googlephone", "description": "Google Phone", "category": "system_app"}, {"package": "com.android.googlepip", "description": "Google Pip", "category": "system_app"}, {"package": "com.android.headsetcontrol", "description": "Headset Control", "category": "system_app"}, {"package": "com.android.incallui", "description": "联系人", "category": "system_app"}, {"package": "com.android.launcher3", "description": "桌面启动器", "category": "system_app"}, {"package": "com.android.leathercase", "description": "Leathercase", "category": "system_app"}, {"package": "com.android.lighteffects", "description": "Light Effects", "category": "system_app"}, {"package": "com.android.magazineservice", "description": "Magazine Service", "category": "system_app"}, {"package": "com.android.microintelligence", "description": "Micro Intelligence", "category": "system_app"}, {"package": "com.android.mol", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.android.multiuser", "description": "Multi User", "category": "system_app"}, {"package": "com.android.music", "description": "音乐播放器", "category": "system_app"}, {"package": "com.android.notes", "description": "备忘录", "category": "system_app"}, {"package": "com.android.onehandedmode", "description": "One Handed Mode", "category": "system_app"}, {"package": "com.android.onetapbutton", "description": "One Tap Button", "category": "system_app"}, {"package": "com.android.oobe", "description": "Oobe", "category": "system_app"}, {"package": "com.android.ossettingsext", "description": "<PERSON><PERSON> Settings Ext", "category": "system_app"}, {"package": "com.android.personalization", "description": "Personalization", "category": "system_app"}, {"package": "com.android.phonemaster", "description": "Phone Master", "category": "system_app"}, {"package": "com.android.privacy", "description": "隐私设置", "category": "system_app"}, {"package": "com.android.questionnaire", "description": "Questionnaire", "category": "system_app"}, {"package": "com.android.recent", "description": "Recent", "category": "system_app"}, {"package": "com.android.recorder", "description": "录音机", "category": "system_app"}, {"package": "com.android.removablespecial", "description": "Removable Special", "category": "system_app"}, {"package": "com.android.salesstatistics", "description": "Sales Statistics", "category": "system_app"}, {"package": "com.android.screenrecord", "description": "Screen Record", "category": "system_app"}, {"package": "com.android.screenshot", "description": "Screen Shot", "category": "system_app"}, {"package": "com.android.settings", "description": "Light Effects", "category": "system_app"}, {"package": "com.android.sharevia", "description": "Sharevia", "category": "system_app"}, {"package": "com.android.smartassistant", "description": "智能助手", "category": "system_app"}, {"package": "com.android.smarthub", "description": "智能中心", "category": "system_app"}, {"package": "com.android.smartmessage", "description": "Smart Message", "category": "system_app"}, {"package": "com.android.smartpanel", "description": "智能面板", "category": "system_app"}, {"package": "com.android.smartscanner", "description": "智能扫描", "category": "system_app"}, {"package": "com.android.smartswitch", "description": "智能切换", "category": "system_app"}, {"package": "com.android.smarttouch", "description": "智能触控", "category": "system_app"}, {"package": "com.android.splitscreen", "description": "Split Screen", "category": "system_app"}, {"package": "com.android.systemui", "description": "Common Control", "category": "system_app"}, {"package": "com.android.theme", "description": "主题", "category": "system_app"}, {"package": "com.android.thunderback", "description": "Thunderback", "category": "system_app"}, {"package": "com.android.tpms", "description": "Tpms", "category": "system_app"}, {"package": "com.android.transid", "description": "Trans Id", "category": "system_app"}, {"package": "com.android.transsionstylus", "description": "Transsion Stylus", "category": "system_app"}, {"package": "com.android.transsiontips", "description": "Transsion Tips", "category": "system_app"}, {"package": "com.android.vending", "description": "Play商店", "category": "system_app"}, {"package": "com.android.visha", "description": "Vishaplayer", "category": "system_app"}, {"package": "com.android.wallpaper", "description": "壁纸", "category": "system_app"}, {"package": "com.android.weather", "description": "天气", "category": "system_app"}, {"package": "com.android.zeroscreen", "description": "Zero Screen", "category": "system_app"}, {"package": "com.autonavi.minimap", "description": "地图", "category": "system_app"}, {"package": "com.baidu.BaiduMap", "description": "地图", "category": "system_app"}, {"package": "com.coloros.settings", "description": "设置", "category": "system_app"}, {"package": "com.es.android.filemanager", "description": "文件管理器", "category": "system_app"}, {"package": "com.facebook.katana", "description": "Facebook", "category": "social"}, {"package": "com.facebook.lite", "description": "Facebook Lite", "category": "social"}, {"package": "com.facebook.mlite", "description": "Facebook", "category": "social"}, {"package": "com.facebook.orca", "description": "Facebook", "category": "social"}, {"package": "com.facebook.pages.app", "description": "Facebook", "category": "social"}, {"package": "com.facebook.work", "description": "Facebook", "category": "social"}, {"package": "com.funbase.xradio", "description": "Fm Radio", "category": "system_app"}, {"package": "com.gallery20", "description": "Ai Gallery", "category": "system_app"}, {"package": "com.google.android.GoogleCamera", "description": "相机", "category": "system_app"}, {"package": "com.google.android.apps.maps", "description": "google地图", "category": "maps"}, {"package": "com.google.android.apps.messaging", "description": "Google Message", "category": "system_app"}, {"package": "com.google.android.apps.nexuslauncher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.google.android.apps.restore", "description": "Google Default", "category": "system_app"}, {"package": "com.google.android.apps.weather", "description": "天气", "category": "system_app"}, {"package": "com.google.android.contacts", "description": "联系人", "category": "system_app"}, {"package": "com.google.android.deskclock", "description": "时钟", "category": "system_app"}, {"package": "com.google.android.dialer", "description": "拨号", "category": "system_app"}, {"package": "com.google.android.documentsui", "description": "文件管理器", "category": "system_app"}, {"package": "com.google.android.gms", "description": "Finder", "category": "system_app"}, {"package": "com.google.android.music", "description": "音乐播放器", "category": "system_app"}, {"package": "com.google.android.youtube", "description": "YouTube", "category": "system_app"}, {"package": "com.hoffnung", "description": "Tpms", "category": "system_app"}, {"package": "com.huawei.android.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.huawei.android.settings", "description": "设置", "category": "system_app"}, {"package": "com.huawei.camera", "description": "相机", "category": "tools"}, {"package": "com.huawei.contacts", "description": "联系人", "category": "communication"}, {"package": "com.huawei.deskclock", "description": "时钟", "category": "tools"}, {"package": "com.huawei.hidisk", "description": "文件管理器", "category": "tools"}, {"package": "com.huawei.music", "description": "音乐播放器", "category": "media"}, {"package": "com.idea.questionnaire", "description": "Questionnaire", "category": "system_app"}, {"package": "com.instagram.android", "description": "Instagram", "category": "social"}, {"package": "com.jingdong.app.mall", "description": "京东", "category": "shopping"}, {"package": "com.kugou.android", "description": "音乐播放器", "category": "system_app"}, {"package": "com.kuwo.kwmusic", "description": "音乐播放器", "category": "media"}, {"package": "com.miui.player", "description": "音乐播放器", "category": "media"}, {"package": "com.miui.securitycenter", "description": "设置", "category": "system_app"}, {"package": "com.miui.weather", "description": "天气", "category": "tools"}, {"package": "com.netease.cloudmusic", "description": "音乐播放器", "category": "media"}, {"package": "com.netflix.mediaclient", "description": "Netflix", "category": "media"}, {"package": "com.oneplus.settings", "description": "设置", "category": "system_app"}, {"package": "com.oppo.camera", "description": "相机", "category": "tools"}, {"package": "com.oppo.clock", "description": "时钟", "category": "tools"}, {"package": "com.oppo.contacts", "description": "联系人", "category": "communication"}, {"package": "com.oppo.filemanager", "description": "文件管理器", "category": "tools"}, {"package": "com.oppo.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.oppo.settings", "description": "设置", "category": "system_app"}, {"package": "com.samsung.android.app.clockpackage", "description": "时钟", "category": "system_app"}, {"package": "com.samsung.android.app.contacts", "description": "联系人", "category": "system_app"}, {"package": "com.samsung.android.app.files", "description": "文件管理器", "category": "system_app"}, {"package": "com.samsung.android.app.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.samsung.android.dialer", "description": "拨号", "category": "system_app"}, {"package": "com.samsung.android.music", "description": "音乐播放器", "category": "system_app"}, {"package": "com.sec.android.app.camera", "description": "相机", "category": "system_app"}, {"package": "com.sec.android.app.settings", "description": "设置", "category": "system_app"}, {"package": "com.sh.smart.aicoremodelmanager", "description": "Ai Core Model Manager", "category": "system_app"}, {"package": "com.sh.smart.aigallery", "description": "Ai Gallery", "category": "system_app"}, {"package": "com.sh.smart.aod", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.sh.smart.calendar", "description": "日历", "category": "tools"}, {"package": "com.sh.smart.caller", "description": "联系人", "category": "communication"}, {"package": "com.sh.smart.childmode", "description": "Childmode", "category": "system_app"}, {"package": "com.sh.smart.clipper", "description": "Clipper", "category": "system_app"}, {"package": "com.sh.smart.commoncontrol", "description": "Common Control", "category": "system_app"}, {"package": "com.sh.smart.cutepet", "description": "Cute Pet", "category": "system_app"}, {"package": "com.sh.smart.demomode", "description": "Demo Mode", "category": "system_app"}, {"package": "com.sh.smart.drivingmode", "description": "Driving Mode", "category": "system_app"}, {"package": "com.sh.smart.dynamicbar", "description": "Dynamic Bar", "category": "system_app"}, {"package": "com.sh.smart.ellascreenrecognition", "description": "Ella Screen Recognition", "category": "system_app"}, {"package": "com.sh.smart.finder", "description": "Finder", "category": "system_app"}, {"package": "com.sh.smart.flipexternalscreen", "description": "Flip External Screen", "category": "system_app"}, {"package": "com.sh.smart.flipmusic", "description": "Flipmusic", "category": "media"}, {"package": "com.sh.smart.fmradio", "description": "Fm Radio", "category": "system_app"}, {"package": "com.sh.smart.foldingscreenzone", "description": "Folding Screen Zone", "category": "system_app"}, {"package": "com.sh.smart.fwdualsystem", "description": "Fw Dual System", "category": "system_app"}, {"package": "com.sh.smart.globalsearch", "description": "Global Search", "category": "system_app"}, {"package": "com.sh.smart.globaltheme", "description": "Global Theme", "category": "system_app"}, {"package": "com.sh.smart.googlecontact", "description": "Google Contact", "category": "communication"}, {"package": "com.sh.smart.googledefault", "description": "Google Default", "category": "system_app"}, {"package": "com.sh.smart.googlemessage", "description": "Google Message", "category": "communication"}, {"package": "com.sh.smart.googlephone", "description": "Google Phone", "category": "communication"}, {"package": "com.sh.smart.googlepip", "description": "Google Pip", "category": "system_app"}, {"package": "com.sh.smart.headsetcontrol", "description": "Headset Control", "category": "system_app"}, {"package": "com.sh.smart.leathercase", "description": "Leathercase", "category": "system_app"}, {"package": "com.sh.smart.lighteffects", "description": "Light Effects", "category": "system_app"}, {"package": "com.sh.smart.magazineservice", "description": "Magazine Service", "category": "system_app"}, {"package": "com.sh.smart.microintelligence", "description": "Micro Intelligence", "category": "system_app"}, {"package": "com.sh.smart.mol", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.sh.smart.multiuser", "description": "Multi User", "category": "system_app"}, {"package": "com.sh.smart.notes", "description": "备忘录", "category": "tools"}, {"package": "com.sh.smart.onehandedmode", "description": "One Handed Mode", "category": "system_app"}, {"package": "com.sh.smart.onetapbutton", "description": "One Tap Button", "category": "system_app"}, {"package": "com.sh.smart.oobe", "description": "Oobe", "category": "system_app"}, {"package": "com.sh.smart.ossettingsext", "description": "<PERSON><PERSON> Settings Ext", "category": "system_app"}, {"package": "com.sh.smart.personalization", "description": "Personalization", "category": "system_app"}, {"package": "com.sh.smart.phonemaster", "description": "Phone Master", "category": "communication"}, {"package": "com.sh.smart.privacy", "description": "隐私设置", "category": "system_app"}, {"package": "com.sh.smart.questionnaire", "description": "Questionnaire", "category": "system_app"}, {"package": "com.sh.smart.recent", "description": "Recent", "category": "system_app"}, {"package": "com.sh.smart.recorder", "description": "录音机", "category": "tools"}, {"package": "com.sh.smart.removablespecial", "description": "Removable Special", "category": "system_app"}, {"package": "com.sh.smart.salesstatistics", "description": "Sales Statistics", "category": "system_app"}, {"package": "com.sh.smart.screenrecord", "description": "Screen Record", "category": "system_app"}, {"package": "com.sh.smart.screenshot", "description": "Screen Shot", "category": "system_app"}, {"package": "com.sh.smart.sharevia", "description": "Sharevia", "category": "system_app"}, {"package": "com.sh.smart.smartassistant", "description": "智能助手", "category": "system_app"}, {"package": "com.sh.smart.smarthub", "description": "智能中心", "category": "system_app"}, {"package": "com.sh.smart.smartmessage", "description": "Smart Message", "category": "communication"}, {"package": "com.sh.smart.smartpanel", "description": "智能面板", "category": "system_app"}, {"package": "com.sh.smart.smartscanner", "description": "智能扫描", "category": "system_app"}, {"package": "com.sh.smart.smartswitch", "description": "智能切换", "category": "system_app"}, {"package": "com.sh.smart.smarttouch", "description": "智能触控", "category": "system_app"}, {"package": "com.sh.smart.splitscreen", "description": "Split Screen", "category": "system_app"}, {"package": "com.sh.smart.systemui", "description": "System Ui", "category": "system_app"}, {"package": "com.sh.smart.theme", "description": "主题", "category": "system_app"}, {"package": "com.sh.smart.thunderback", "description": "Thunderback", "category": "system_app"}, {"package": "com.sh.smart.tpms", "description": "Tpms", "category": "system_app"}, {"package": "com.sh.smart.transid", "description": "Trans Id", "category": "system_app"}, {"package": "com.sh.smart.transsionstylus", "description": "Transsion Stylus", "category": "system_app"}, {"package": "com.sh.smart.transsiontips", "description": "Transsion Tips", "category": "system_app"}, {"package": "com.sh.smart.visha", "description": "Vishaplayer", "category": "system_app"}, {"package": "com.sh.smart.wallpaper", "description": "壁纸", "category": "system_app"}, {"package": "com.sh.smart.zeroscreen", "description": "Zero Screen", "category": "system_app"}, {"package": "com.sina.weibo", "description": "微博", "category": "social"}, {"package": "com.smile.gifmaker", "description": "快手", "category": "media"}, {"package": "com.sogou.map.android.maps", "description": "地图", "category": "system_app"}, {"package": "com.spotify.music", "description": "Spotify", "category": "media"}, {"package": "com.ss.android.ugc.aweme", "description": "抖音", "category": "media"}, {"package": "com.taobao.taobao", "description": "淘宝", "category": "shopping"}, {"package": "com.tencent.ig", "description": "和平精英", "category": "game"}, {"package": "com.tencent.map", "description": "地图", "category": "system_app"}, {"package": "com.tencent.mm", "description": "微信", "category": "social"}, {"package": "com.tencent.mobileqq", "description": "QQ", "category": "social"}, {"package": "com.tencent.qqmusic", "description": "音乐播放器", "category": "social"}, {"package": "com.tencent.tmgp.sgame", "description": "王者荣耀", "category": "game"}, {"package": "com.transsion.aicore.main", "description": "Ai Core Model Manager", "category": "system_app"}, {"package": "com.transsion.aicoremodelmanager", "description": "Ai Core Model Manager", "category": "system_app"}, {"package": "com.transsion.aigallery", "description": "Ai Gallery", "category": "system_app"}, {"package": "com.transsion.aivoiceassistant", "description": "Ella语音助手", "category": "system_app"}, {"package": "com.transsion.aod", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.transsion.calculator", "description": "计算器", "category": "system_app"}, {"package": "com.transsion.calendar", "description": "日历", "category": "system_app"}, {"package": "com.transsion.camera", "description": "相机", "category": "system_app"}, {"package": "com.transsion.carlcare", "description": "Carlcare", "category": "system_app"}, {"package": "com.transsion.childmode", "description": "Childmode", "category": "system_app"}, {"package": "com.transsion.clipper", "description": "Clipper", "category": "system_app"}, {"package": "com.transsion.cloudserver", "description": "Trans Id", "category": "system_app"}, {"package": "com.transsion.commoncontrol", "description": "Common Control", "category": "system_app"}, {"package": "com.transsion.cutepet", "description": "Cute Pet", "category": "system_app"}, {"package": "com.transsion.demomode", "description": "Demo Mode", "category": "system_app"}, {"package": "com.transsion.deskclock", "description": "时钟", "category": "system_app"}, {"package": "com.transsion.dialer", "description": "拨号", "category": "system_app"}, {"package": "com.transsion.drivingmode", "description": "Driving Mode", "category": "system_app"}, {"package": "com.transsion.dynamicbar", "description": "Dynamic Bar", "category": "system_app"}, {"package": "com.transsion.ellascreenrecognition", "description": "Ella Screen Recognition", "category": "system_app"}, {"package": "com.transsion.filemanager", "description": "文件管理器", "category": "system_app"}, {"package": "com.transsion.finder", "description": "Finder", "category": "system_app"}, {"package": "com.transsion.flipexternalscreen", "description": "Flip External Screen", "category": "system_app"}, {"package": "com.transsion.flipmusic", "description": "Flipmusic", "category": "system_app"}, {"package": "com.transsion.fmradio", "description": "Fm Radio", "category": "system_app"}, {"package": "com.transsion.foldingscreenzone", "description": "Folding Screen Zone", "category": "system_app"}, {"package": "com.transsion.fwdualsystem", "description": "Fw Dual System", "category": "system_app"}, {"package": "com.transsion.globalsearch", "description": "Global Search", "category": "system_app"}, {"package": "com.transsion.globaltheme", "description": "Global Theme", "category": "system_app"}, {"package": "com.transsion.googlecontact", "description": "Google Contact", "category": "system_app"}, {"package": "com.transsion.googledefault", "description": "Google Default", "category": "system_app"}, {"package": "com.transsion.googlemessage", "description": "Google Message", "category": "system_app"}, {"package": "com.transsion.googlephone", "description": "Google Phone", "category": "system_app"}, {"package": "com.transsion.googlepip", "description": "Google Pip", "category": "system_app"}, {"package": "com.transsion.headsetcontrol", "description": "Headset Control", "category": "system_app"}, {"package": "com.transsion.healthlife", "description": "Healthlife", "category": "system_app"}, {"package": "com.transsion.hilauncher", "description": "Global Search", "category": "system_app"}, {"package": "com.transsion.kolun.assistant", "description": "智能助手", "category": "system_app"}, {"package": "com.transsion.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.transsion.leathercase", "description": "Leathercase", "category": "system_app"}, {"package": "com.transsion.letswitch", "description": "智能切换", "category": "system_app"}, {"package": "com.transsion.lighteffects", "description": "Light Effects", "category": "system_app"}, {"package": "com.transsion.livewallpaper.colorart", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.fantasy", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.micro", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.mondrian", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.page", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.pictorial", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.volcano", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.livewallpaper.wakeup_mirror", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.magazineservice", "description": "Magazine Service", "category": "system_app"}, {"package": "com.transsion.magicshow", "description": "<PERSON><PERSON><PERSON>", "category": "system_app"}, {"package": "com.transsion.microintelligence", "description": "Micro Intelligence", "category": "system_app"}, {"package": "com.transsion.mol", "description": "<PERSON><PERSON>", "category": "system_app"}, {"package": "com.transsion.multiuser", "description": "Multi User", "category": "system_app"}, {"package": "com.transsion.notebook", "description": "备忘录", "category": "system_app"}, {"package": "com.transsion.notes", "description": "备忘录", "category": "system_app"}, {"package": "com.transsion.onehandedmode", "description": "One Handed Mode", "category": "system_app"}, {"package": "com.transsion.onetapbutton", "description": "One Tap Button", "category": "system_app"}, {"package": "com.transsion.oobe", "description": "Oobe", "category": "system_app"}, {"package": "com.transsion.os.typeface", "description": "Personalization", "category": "system_app"}, {"package": "com.transsion.ossettingsext", "description": "<PERSON><PERSON> Settings Ext", "category": "system_app"}, {"package": "com.transsion.overlaysuw", "description": "Oobe", "category": "system_app"}, {"package": "com.transsion.personalization", "description": "Personalization", "category": "system_app"}, {"package": "com.transsion.phonemaster", "description": "Phone Master", "category": "system_app"}, {"package": "com.transsion.privacy", "description": "隐私设置", "category": "system_app"}, {"package": "com.transsion.questionnaire", "description": "Questionnaire", "category": "system_app"}, {"package": "com.transsion.recent", "description": "Recent", "category": "system_app"}, {"package": "com.transsion.recorder", "description": "录音机", "category": "system_app"}, {"package": "com.transsion.removablespecial", "description": "Removable Special", "category": "system_app"}, {"package": "com.transsion.resolver", "description": "Sharevia", "category": "system_app"}, {"package": "com.transsion.salesstatistics", "description": "Sales Statistics", "category": "system_app"}, {"package": "com.transsion.scanningrecharger", "description": "智能扫描", "category": "system_app"}, {"package": "com.transsion.screencapture", "description": "Screen Shot", "category": "system_app"}, {"package": "com.transsion.screenrecord", "description": "Screen Record", "category": "system_app"}, {"package": "com.transsion.screenrecorder", "description": "Screen Record", "category": "system_app"}, {"package": "com.transsion.screenshot", "description": "Screen Shot", "category": "system_app"}, {"package": "com.transsion.settings", "description": "设置", "category": "system_app"}, {"package": "com.transsion.settings.flexbutton", "description": "One Tap Button", "category": "system_app"}, {"package": "com.transsion.sharevia", "description": "Sharevia", "category": "system_app"}, {"package": "com.transsion.smartassistant", "description": "智能助手", "category": "system_app"}, {"package": "com.transsion.smarthub", "description": "智能中心", "category": "system_app"}, {"package": "com.transsion.smartmessage", "description": "Smart Message", "category": "system_app"}, {"package": "com.transsion.smartpanel", "description": "智能面板", "category": "system_app"}, {"package": "com.transsion.smartrecognition", "description": "Ella Screen Recognition", "category": "system_app"}, {"package": "com.transsion.smartscanner", "description": "智能扫描", "category": "system_app"}, {"package": "com.transsion.smartswitch", "description": "智能切换", "category": "system_app"}, {"package": "com.transsion.smarttouch", "description": "智能触控", "category": "system_app"}, {"package": "com.transsion.soundrecorder", "description": "录音机", "category": "system_app"}, {"package": "com.transsion.spacesaversdk", "description": "Removable Special", "category": "system_app"}, {"package": "com.transsion.splitscreen", "description": "Split Screen", "category": "system_app"}, {"package": "com.transsion.statisticalsales", "description": "Sales Statistics", "category": "system_app"}, {"package": "com.transsion.stylus", "description": "Transsion Stylus", "category": "system_app"}, {"package": "com.transsion.subsystemui", "description": "Flip External Screen", "category": "system_app"}, {"package": "com.transsion.systemui", "description": "System Ui", "category": "system_app"}, {"package": "com.transsion.theme", "description": "Global Theme", "category": "system_app"}, {"package": "com.transsion.thunderback", "description": "Thunderback", "category": "system_app"}, {"package": "com.transsion.tips", "description": "Transsion Tips", "category": "system_app"}, {"package": "com.transsion.tpms", "description": "Tpms", "category": "system_app"}, {"package": "com.transsion.tranhall", "description": "Leathercase", "category": "system_app"}, {"package": "com.transsion.transid", "description": "Trans Id", "category": "system_app"}, {"package": "com.transsion.transsionstylus", "description": "Transsion Stylus", "category": "system_app"}, {"package": "com.transsion.transsiontips", "description": "Transsion Tips", "category": "system_app"}, {"package": "com.transsion.visha", "description": "音乐播放器", "category": "system_app"}, {"package": "com.transsion.vishaplayer", "description": "Vishaplayer", "category": "system_app"}, {"package": "com.transsion.wallpaper", "description": "壁纸", "category": "system_app"}, {"package": "com.transsion.weather", "description": "天气", "category": "system_app"}, {"package": "com.transsion.zahooc", "description": "隐私设置", "category": "system_app"}, {"package": "com.transsion.zeroscreen", "description": "Zero Screen", "category": "system_app"}, {"package": "com.twitter.android", "description": "Twitter", "category": "social"}, {"package": "com.visha", "description": "音乐播放器", "category": "system_app"}, {"package": "com.visha.music", "description": "音乐播放器", "category": "media"}, {"package": "com.visha.player", "description": "音乐播放器", "category": "media"}, {"package": "com.vivo.camera", "description": "相机", "category": "tools"}, {"package": "com.vivo.clock", "description": "时钟", "category": "tools"}, {"package": "com.vivo.contacts", "description": "联系人", "category": "communication"}, {"package": "com.vivo.filemanager", "description": "文件管理器", "category": "tools"}, {"package": "com.vivo.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.vivo.settings", "description": "设置", "category": "system_app"}, {"package": "com.weather.channel", "description": "天气", "category": "tools"}, {"package": "com.weather.forecast", "description": "天气", "category": "tools"}, {"package": "com.whatsapp", "description": "WhatsApp", "category": "social"}, {"package": "com.xiaomi.camera", "description": "相机", "category": "tools"}, {"package": "com.xiaomi.contacts", "description": "联系人", "category": "communication"}, {"package": "com.xiaomi.deskclock", "description": "时钟", "category": "tools"}, {"package": "com.xiaomi.fileexplorer", "description": "文件管理器", "category": "tools"}, {"package": "com.xiaomi.launcher", "description": "桌面启动器", "category": "system_app"}, {"package": "com.xiaomi.misettings", "description": "设置", "category": "system_app"}], "recent_apps_clear_positions": [{"x": 540, "y": 1800, "description": "底部中央清理按钮", "priority": 1}, {"x": 1000, "y": 1800, "description": "底部右侧清理按钮", "priority": 2}, {"x": 200, "y": 1800, "description": "底部左侧清理按钮", "priority": 3}, {"x": 540, "y": 200, "description": "顶部中央清理按钮", "priority": 4}, {"x": 1000, "y": 200, "description": "顶部右侧清理按钮", "priority": 5}, {"x": 800, "y": 1600, "description": "右下角清理按钮", "priority": 6}, {"x": 540, "y": 1600, "description": "中下清理按钮", "priority": 7}, {"x": 300, "y": 1600, "description": "左下清理按钮", "priority": 8}, {"x": 540, "y": 400, "description": "中上清理按钮", "priority": 9}, {"x": 900, "y": 1000, "description": "右中清理按钮", "priority": 10}], "cleanup_settings": {"command_cleanup_enabled": true, "recent_apps_fallback_enabled": true, "recent_apps_priority": false, "min_apps_for_fallback": 3, "cleanup_timeout": 8, "stabilization_wait": 2, "max_retry_attempts": 3, "force_stop_enabled": true, "recent_cleanup_wait": 3, "ui_detection_enabled": true, "enhanced_tap_enabled": true, "backup_methods_enabled": true, "page_load_wait": 2, "tap_interval": 0.8, "verification_enabled": true, "multi_gesture_enabled": true, "system_cleanup_enabled": true, "category_cleanup_enabled": true, "running_apps_detection": true}}}