#!/usr/bin/env python3
"""
提取所有检测器的包名信息并更新 process_cleanup_config.json
"""

import os
import json
import importlib.util
import sys
from typing import List, Dict, Set
from pathlib import Path

def load_detector_module(detector_file: Path):
    """动态加载检测器模块"""
    try:
        spec = importlib.util.spec_from_file_location(
            detector_file.stem, detector_file
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        print(f"加载模块 {detector_file} 失败: {e}")
        return None

def extract_packages_from_detectors() -> Dict[str, List[str]]:
    """从所有检测器中提取包名"""
    detectors_dir = Path("pages/base/detectors")
    packages_info = {}
    
    # 遍历所有检测器文件
    for detector_file in detectors_dir.glob("*_detector.py"):
        if detector_file.name.startswith("__"):
            continue
            
        print(f"处理检测器: {detector_file.name}")
        
        # 动态加载模块
        module = load_detector_module(detector_file)
        if not module:
            continue
            
        # 查找检测器类
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (isinstance(attr, type) and 
                attr_name.endswith('Detector') and 
                hasattr(attr, 'get_package_names')):
                
                try:
                    # 创建实例并获取包名
                    detector_instance = attr()
                    package_names = detector_instance.get_package_names()
                    
                    if package_names:
                        detector_name = detector_file.stem.replace('_detector', '')
                        packages_info[detector_name] = package_names
                        print(f"  - 找到 {len(package_names)} 个包名")
                        
                except Exception as e:
                    print(f"  - 实例化失败: {e}")
                    
    return packages_info

def categorize_package(package_name: str, detector_name: str) -> str:
    """根据包名和检测器名称确定应用类别"""
    # 系统应用
    if any(keyword in package_name.lower() for keyword in [
        'transsion', 'android.', 'systemui', 'settings', 'launcher'
    ]):
        return "system_app"
    
    # 社交应用
    if any(keyword in package_name.lower() for keyword in [
        'facebook', 'whatsapp', 'instagram', 'twitter', 'weibo', 'qq', 'wechat'
    ]):
        return "social"
    
    # 媒体应用
    if any(keyword in package_name.lower() for keyword in [
        'music', 'video', 'player', 'spotify', 'netflix', 'youtube'
    ]):
        return "media"
    
    # 浏览器
    if any(keyword in package_name.lower() for keyword in [
        'chrome', 'browser', 'ucmobile'
    ]):
        return "browser"
    
    # 地图导航
    if any(keyword in package_name.lower() for keyword in [
        'maps', 'navigation'
    ]):
        return "maps"
    
    # 购物应用
    if any(keyword in package_name.lower() for keyword in [
        'taobao', 'jingdong', 'shopping'
    ]):
        return "shopping"
    
    # 游戏应用
    if any(keyword in package_name.lower() for keyword in [
        'game', 'tmgp'
    ]):
        return "game"
    
    # 工作应用
    if any(keyword in package_name.lower() for keyword in [
        'rimet', 'office', 'work'
    ]):
        return "work"
    
    # 通信应用
    if any(keyword in detector_name.lower() for keyword in [
        'dialer', 'contact', 'message', 'phone'
    ]):
        return "communication"
    
    # 工具应用
    if any(keyword in detector_name.lower() for keyword in [
        'calculator', 'calendar', 'clock', 'alarm', 'notes', 'recorder',
        'camera', 'file', 'weather'
    ]):
        return "tools"
    
    # 默认为系统应用
    return "system_app"

def get_app_description(package_name: str, detector_name: str) -> str:
    """根据包名和检测器名称生成应用描述"""
    # 特殊应用的描述映射
    special_descriptions = {
        'com.transsion.aivoiceassistant': 'Ella语音助手',
        'com.transsion.magicshow': 'Visha音乐播放器',
        'com.android.chrome': 'Chrome浏览器',
        'com.facebook.katana': 'Facebook',
        'com.whatsapp': 'WhatsApp',
        'com.instagram.android': 'Instagram',
        'com.twitter.android': 'Twitter',
        'com.spotify.music': 'Spotify',
        'com.netflix.mediaclient': 'Netflix',
        'com.tencent.mm': '微信',
        'com.alibaba.android.rimet': '钉钉',
        'com.tencent.mobileqq': 'QQ',
        'com.sina.weibo': '微博',
        'com.taobao.taobao': '淘宝',
        'com.jingdong.app.mall': '京东',
        'com.tencent.tmgp.sgame': '王者荣耀',
        'com.tencent.ig': '和平精英',
        'com.ss.android.ugc.aweme': '抖音',
        'com.smile.gifmaker': '快手',
        'com.google.android.apps.maps': 'Google地图',
        'com.UCMobile': 'UC浏览器',
    }
    
    if package_name in special_descriptions:
        return special_descriptions[package_name]
    
    # 根据检测器名称生成描述
    detector_descriptions = {
        'ella': 'Ella语音助手',
        'camera': '相机',
        'settings': '设置',
        'contacts': '联系人',
        'music': '音乐播放器',
        'weather': '天气',
        'calculator': '计算器',
        'calendar': '日历',
        'clock': '时钟',
        'alarm': '闹钟',
        'notes': '备忘录',
        'recorder': '录音机',
        'maps': '地图',
        'playstore': 'Play商店',
        'youtube': 'YouTube',
        'whatsapp': 'WhatsApp',
        'facebook': 'Facebook',
        'dialer': '拨号',
        'launcher': '桌面启动器',
        'file_manager': '文件管理器',
        'theme': '主题',
        'wallpaper': '壁纸',
        'privacy': '隐私设置',
        'smart_assistant': '智能助手',
        'smart_panel': '智能面板',
        'smart_scanner': '智能扫描',
        'smart_switch': '智能切换',
        'smart_touch': '智能触控',
        'smart_hub': '智能中心',
    }
    
    if detector_name in detector_descriptions:
        return detector_descriptions[detector_name]
    
    # 默认描述
    return detector_name.replace('_', ' ').title()

def update_config_file(packages_info: Dict[str, List[str]]):
    """更新配置文件"""
    config_file = Path("config/process_cleanup_config.json")
    
    # 读取现有配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 获取现有包名，避免重复
    existing_packages = set()
    for app in config['process_cleanup_config']['common_user_apps']:
        existing_packages.add(app['package'])
    
    # 添加新的包名
    new_apps = []
    for detector_name, package_names in packages_info.items():
        for package_name in package_names:
            if package_name not in existing_packages:
                app_info = {
                    "package": package_name,
                    "description": get_app_description(package_name, detector_name),
                    "category": categorize_package(package_name, detector_name)
                }
                new_apps.append(app_info)
                existing_packages.add(package_name)
    
    # 将新应用添加到配置中
    config['process_cleanup_config']['common_user_apps'].extend(new_apps)
    
    # 按包名排序
    config['process_cleanup_config']['common_user_apps'].sort(
        key=lambda x: x['package']
    )
    
    # 写回配置文件
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 配置文件已更新")
    print(f"   - 新增 {len(new_apps)} 个应用包名")
    print(f"   - 总计 {len(config['process_cleanup_config']['common_user_apps'])} 个应用")

def main():
    """主函数"""
    print("🔍 开始提取检测器包名信息...")
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        # 提取包名信息
        packages_info = extract_packages_from_detectors()
        
        if not packages_info:
            print("❌ 未找到任何包名信息")
            return
        
        print(f"\n📊 提取结果:")
        total_packages = 0
        for detector_name, package_names in packages_info.items():
            print(f"   - {detector_name}: {len(package_names)} 个包名")
            total_packages += len(package_names)
        
        print(f"\n总计: {len(packages_info)} 个检测器，{total_packages} 个包名")
        
        # 更新配置文件
        update_config_file(packages_info)
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
